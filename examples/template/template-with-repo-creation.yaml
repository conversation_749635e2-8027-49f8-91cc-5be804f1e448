apiVersion: scaffolder.backstage.io/v1beta3
kind: Template
metadata:
  name: enhanced-nodejs-template
  title: Enhanced Node.js Template with Repo Creation
  description: An enhanced template that provides better repository creation handling and user feedback
spec:
  owner: user:guest
  type: service

  parameters:
    - title: Project Information
      required:
        - name
        - description
      properties:
        name:
          title: Repository Name
          type: string
          description: The name of the repository to create (must be unique)
          ui:autofocus: true
          ui:options:
            rows: 1
        description:
          title: Description
          type: string
          description: A short description of the service
        owner:
          title: Repository Owner
          type: string
          description: GitHub username or organization name
          default: your-username-here
    - title: Repository Settings
      required:
        - repoVisibility
      properties:
        repoVisibility:
          title: Repository Visibility
          type: string
          enum: ['public', 'private']
          default: 'public'
          description: Whether the repository should be public or private
        includeTopics:
          title: Include Topics
          type: boolean
          default: true
          description: Add relevant topics to the repository
    - title: Advanced Options
      properties:
        enableIssues:
          title: Enable Issues
          type: boolean
          default: true
        enableWiki:
          title: Enable Wiki
          type: boolean
          default: false
        enableProjects:
          title: Enable Projects
          type: boolean
          default: true

  steps:
    - id: log-start
      name: Log Start
      action: debug:log
      input:
        message: "Starting template creation for ${{ parameters.name }}"

    - id: validate-owner
      name: Validate Repository Owner
      action: debug:log
      input:
        message: "Creating repository under owner: ${{ parameters.owner }}"

    - id: fetch-base
      name: Fetch Base Template
      action: fetch:template
      input:
        url: ./content
        values:
          name: ${{ parameters.name }}
          description: ${{ parameters.description }}

    - id: publish
      name: Create and Publish Repository
      action: publish:github
      input:
        description: ${{ parameters.description }}
        repoUrl: "github.com?owner=${{ parameters.owner }}&repo=${{ parameters.name }}"
        defaultBranch: 'main'
        repoVisibility: ${{ parameters.repoVisibility }}
        deleteBranchOnMerge: true
        allowRebaseMerge: true
        allowSquashMerge: true
        allowMergeCommit: true
        requireCodeOwnerReviews: false
        homepage: "https://github.com/${{ parameters.owner }}/${{ parameters.name }}"
        topics: ${{ parameters.includeTopics && ['backstage', 'nodejs', 'service', 'microservice'] || [] }}
        hasIssues: ${{ parameters.enableIssues }}
        hasWiki: ${{ parameters.enableWiki }}
        hasProjects: ${{ parameters.enableProjects }}

    - id: log-success
      name: Log Success
      action: debug:log
      input:
        message: "Successfully created repository ${{ parameters.owner }}/${{ parameters.name }}"

    - id: register
      name: Register Component
      action: catalog:register
      input:
        repoContentsUrl: ${{ steps['publish'].output.repoContentsUrl }}
        catalogInfoPath: '/catalog-info.yaml'

  output:
    links:
      - title: Repository
        url: ${{ steps['publish'].output.remoteUrl }}
        icon: github
      - title: Open in catalog
        url: ${{ steps['register'].output.entityRef | catalogEntityUrl }}
        icon: catalog
    text:
      - title: Summary
        content: |
          ## 🎉 Successfully created **${{ parameters.name }}**
          
          **Repository:** ${{ steps['publish'].output.remoteUrl }}
          **Owner:** ${{ parameters.owner }}
          **Visibility:** ${{ parameters.repoVisibility }}
          
          Your new Node.js service has been created and is ready for development!